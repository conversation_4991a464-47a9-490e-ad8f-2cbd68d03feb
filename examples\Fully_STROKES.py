from torcheeg.datasets import StrokePatientsMIDataset, StrokePatientsMIProcessedDataset
from torcheeg import transforms
from torcheeg.datasets.constants.motor_imagery import STROKEPATIENTSMI_LOCATION_DICT
from torcheeg.model_selection import train_test_split_cross_subject, train_test_split_cross_trial, \
    train_test_split_groupby_trial
from torch.utils.data import DataLoader

from torcheeg.models import CCNN, GETN
from torcheeg.trainers import ClassifierTrainer
import pytorch_lightning as pl
from pytorch_lightning.strategies import DDPStrategy

dataset = StrokePatientsMIDataset(
    root_path='../dataset/StrokePatientsMIDataset',
    io_path='./Fully_STROKES/strokes',
    offline_transform=transforms.Compose([
        # transforms.MinMaxNormalize(axis=-1, apply_to_baseline=True),
        transforms.BandDifferentialEntropy(sampling_rate=500, apply_to_baseline=True),
        # transforms.ToGrid(STROKEPATIENTSMI_LOCATION_DICT, apply_to_baseline=True),
        # transforms.To2d(),
    ]),
    online_transform=transforms.Compose([
        transforms.BaselineRemoval(),
        transforms.ToTensor()
    ]),
    label_transform=transforms.Select('label'),
    num_worker=8,
    verbose=False
)

train_dataset, val_dataset = train_test_split_groupby_trial(
    dataset=dataset,
    test_size=0.2,
    split_path=f'./Fully_STROKES/split/strokes',
    shuffle=True,
    random_state=42
)

train_loader = DataLoader(train_dataset, batch_size=32, num_workers=8, shuffle=True)
val_loader = DataLoader(val_dataset, batch_size=32, num_workers=8, shuffle=False)

# model = CCNN(
#     num_classes=10, in_channels=4, grid_size=(8, 9)
# )

model = GETN(in_channels=4, num_electrodes=30,
             cheby_out_channels=16, cheby_num_layers=1,
             num_classes=2, hidden_dim=128,
             num_heads=8, num_layers=2,
             dropout=0.3)

for batch in train_loader:
    x, y = batch
    print(f"训练数据形状: {[x.shape for x in x]}")
    print(f"标签形状: {y.shape}")
    # 检查特征提取器输出
#     with torch.no_grad():
#         features = model(x[0])
#         print(f"特征提取器输出形状: {features.shape}")
#     break

trainer = ClassifierTrainer(model=model,
                            num_classes=2,
                            lr=1e-4,
                            weight_decay=1e-4,
                            devices=10,
                            accelerator="gpu",
                            # metrics=['accuracy', 'precision',
                            #          'recall', 'f1score', 'auroc']
                            )

trainer.fit(train_loader,
            val_loader,
            max_epochs=200,
            strategy=DDPStrategy(find_unused_parameters=True),
            default_root_dir=f'./Fully_STROKES/model',
            callbacks=[pl.callbacks.ModelCheckpoint(save_last=True)],
            enable_progress_bar=True,
            enable_model_summary=True,
            limit_val_batches=1.0)

score = trainer.test(val_loader,
                     enable_progress_bar=True,
                     enable_model_summary=True,)[0]

print(f'Test accuracy: {score["test_accuracy"]:.4f}')