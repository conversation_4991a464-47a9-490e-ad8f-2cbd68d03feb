from torcheeg.datasets import BCICIV2aDataset
from torcheeg import transforms
from torcheeg.model_selection import train_test_split_cross_trial, train_test_split_cross_subject, train_test_split_groupby_trial
from torch.utils.data import DataLoader
from torcheeg.datasets.constants.motor_imagery.bciciv_2a import BCICIV2A_LOCATION_DICT
from torcheeg.models import Conformer, LaBraM, ViT, CCNN, GETN
import pytorch_lightning as pl
from pytorch_lightning.strategies import DDPStrategy

from torcheeg.trainers import ClassifierTrainer

dataset = BCICIV2aDataset(root_path='../dataset/BCICIV_2a_mat',
                          io_path=f'./Fully_BCICIV_2a/bciciv_2a',
                          offline_transform=transforms.Compose([
                              transforms.MinMaxNormalize(axis=-1),
                              transforms.To2d()
                          ]),
                          online_transform=transforms.Compose([
                              transforms.ToTensor()
                          ]),
                          label_transform=transforms.Compose([
                              transforms.Select('label'),
                              transforms.Lambda(lambda x: x - 1)
                          ]),
                          num_worker=8,
                          verbose=False)

train_dataset, val_dataset = train_test_split_groupby_trial(
    dataset=dataset,
    test_size=0.2,
    split_path=f'./Fully_BCICIV_2a/split/bciciv_2a',
    shuffle=True,
    random_state=42
)

train_loader = DataLoader(train_dataset, batch_size=64, num_workers=8, shuffle=True)
val_loader = DataLoader(val_dataset, batch_size=64, num_workers=8, shuffle=False)

# model = GETN(in_channels=4, num_electrodes=22,
#              cheby_out_channels=16, cheby_num_layers=1,
#              num_classes=4, hidden_dim=128,
#              num_heads=8, num_layers=2,
#              dropout=0.3)

model = Conformer(num_electrodes=22,
                  sampling_rate=7 * 250,
                  hid_channels=40,
                  depth=6,
                  heads=10,
                  dropout=0.5,
                  forward_expansion=4,
                  forward_dropout=0.5,
                  num_classes=4)

for batch in train_loader:
    x, y = batch
    print(f"训练数据形状: {[x.shape for x in x]}")
    print(f"标签形状: {y.shape}")
    # 检查特征提取器输出
    # with torch.no_grad():
    #     features = model(x[0])
    #     print(f"特征提取器输出形状: {features.shape}")
    # break

trainer = ClassifierTrainer(model=model,
                            num_classes=4,
                            lr=1e-4,
                            weight_decay=1e-4,
                            devices=10,
                            accelerator="gpu",
                            # metrics=['accuracy', 'precision',
                            #          'recall', 'f1score', 'auroc']
                            )

trainer.fit(train_loader,
            val_loader,
            max_epochs=100,
            strategy=DDPStrategy(find_unused_parameters=True),
            default_root_dir=f'./Fully_BCICIV_2a/model',
            callbacks=[pl.callbacks.ModelCheckpoint(save_last=True)],
            enable_progress_bar=True,
            enable_model_summary=True,
            limit_val_batches=1.0)

score = trainer.test(val_loader,
                     enable_progress_bar=True,
                     enable_model_summary=True, )[0]

print(f'Test accuracy: {score["test_accuracy"]:.4f}')
