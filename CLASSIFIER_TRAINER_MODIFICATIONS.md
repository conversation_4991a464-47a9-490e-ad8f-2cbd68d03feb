# ClassifierTrainer 修改说明

## 概述

根据您的要求，我已经修改了 `torcheeg/trainers/classifier.py` 中的 `ClassifierTrainer` 类，使其能够根据分类任务类型自动选择合适的评估指标：

- **二分类任务** (num_classes=2): 计算平衡准确率、AUC-PR 和 AUROC
- **多分类任务** (num_classes>2): 计算平衡准确率、<PERSON>'s Kappa 和加权 F1 分数

## 主要修改

### 1. 扩展了 `classification_metrics` 函数

**新增的指标支持：**
- `balanced_accuracy`: 平衡准确率 (使用 `torchmetrics.Accuracy` 的 `average='macro'`)
- `average_precision`: AUC-PR (使用 `torchmetrics.AveragePrecision`)
- `weighted_f1`: 加权 F1 分数 (使用 `torchmetrics.F1Score` 的 `average='weighted'`)

**改进的任务类型检测：**
- 自动根据 `num_classes` 确定任务类型 (`binary` vs `multiclass`)
- 为所有指标正确设置 `task` 参数

### 2. 新增 `get_default_metrics_for_task` 函数

```python
def get_default_metrics_for_task(num_classes: int) -> List[str]:
    if num_classes == 2:
        # 二分类: 平衡准确率, AUC-PR, AUROC
        return ['balanced_accuracy', 'average_precision', 'auroc']
    else:
        # 多分类: 平衡准确率, Cohen's Kappa, 加权 F1
        return ['balanced_accuracy', 'kappa', 'weighted_f1']
```

### 3. 修改 `ClassifierTrainer` 类

**参数变更：**
- `metrics` 参数默认值从 `["accuracy"]` 改为 `None`
- 当 `metrics=None` 时，自动根据任务类型选择指标

**自动指标选择逻辑：**
```python
if metrics is None:
    self.metrics = get_default_metrics_for_task(num_classes)
    if verbose:
        task_type = "binary" if num_classes == 2 else "multi-class"
        log.info(f"Auto-selected metrics for {task_type} classification: {self.metrics}")
else:
    self.metrics = metrics
```

**文档更新：**
- 更新了类的文档字符串，说明自动指标选择功能
- 列出了所有可用的指标选项

## 使用方法

### 二分类任务 (自动选择指标)

```python
from torcheeg.trainers import ClassifierTrainer
from torcheeg.models import CCNN

model = CCNN(in_channels=62, num_classes=2)
trainer = ClassifierTrainer(
    model=model,
    num_classes=2,  # 二分类
    lr=1e-4,
    weight_decay=1e-4
)

# 自动选择的指标: ['balanced_accuracy', 'average_precision', 'auroc']
trainer.fit(train_loader, val_loader)
results = trainer.test(test_loader)
```

### 多分类任务 (自动选择指标)

```python
model = CCNN(in_channels=62, num_classes=4)
trainer = ClassifierTrainer(
    model=model,
    num_classes=4,  # 多分类
    lr=1e-4,
    weight_decay=1e-4
)

# 自动选择的指标: ['balanced_accuracy', 'kappa', 'weighted_f1']
trainer.fit(train_loader, val_loader)
results = trainer.test(test_loader)
```

### 自定义指标 (覆盖自动选择)

```python
custom_metrics = ['accuracy', 'f1score', 'auroc', 'precision']
trainer = ClassifierTrainer(
    model=model,
    num_classes=2,
    metrics=custom_metrics  # 使用自定义指标
)
```

## 指标说明

### 二分类指标

1. **平衡准确率 (balanced_accuracy)**
   - 计算每个类别准确率的平均值
   - 对类别不平衡数据集更加公平

2. **AUC-PR (average_precision)**
   - Precision-Recall 曲线下面积
   - 对不平衡数据集比 AUROC 更敏感

3. **AUROC (auroc)**
   - ROC 曲线下面积
   - 经典的二分类评估指标

### 多分类指标

1. **平衡准确率 (balanced_accuracy)**
   - 同二分类，计算每个类别准确率的平均值

2. **Cohen's Kappa (kappa)**
   - 衡量分类器与随机分类器的一致性
   - 考虑了偶然一致性的影响

3. **加权 F1 分数 (weighted_f1)**
   - 按类别样本数量加权的 F1 分数
   - 适合处理类别不平衡的多分类问题

## 向后兼容性

- 现有代码仍然可以正常工作
- 如果明确指定 `metrics` 参数，将使用指定的指标
- 只有当 `metrics=None` (默认值) 时才会自动选择指标

## 测试

修改包含了完整的测试用例和使用示例：
- `test_classifier_trainer.py`: 单元测试
- `classifier_trainer_usage_example.py`: 使用示例

所有修改都经过了语法检查，确保代码的正确性。
